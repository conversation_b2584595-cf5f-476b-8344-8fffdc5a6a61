// app.js
App({
  globalData: {
    userInfo: null,
    systemInfo: null,
    statusBarHeight: 0,
    navBarHeight: 0
  },

  onLaunch() {
    // 获取系统信息
    this.getSystemInfo();
    
    // 检查更新
    this.checkUpdate();
    
    // 初始化用户信息
    this.initUserInfo();
  },

  onShow() {
    console.log('App Show');
  },

  onHide() {
    console.log('App Hide');
  },

  onError(msg) {
    console.error('App Error:', msg);
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const { statusBarHeight, platform, model, system } = systemInfo;
      
      // 计算导航栏高度
      let navBarHeight = 44;
      if (platform === 'ios') {
        // iPhone X 系列适配
        if (model.includes('iPhone X') || model.includes('iPhone 11') || 
            model.includes('iPhone 12') || model.includes('iPhone 13') ||
            model.includes('iPhone 14') || model.includes('iPhone 15') ||
            model.includes('iPhone 16')) {
          navBarHeight = 44;
        } else {
          navBarHeight = 44;
        }
      } else if (platform === 'android') {
        navBarHeight = 48;
      }

      this.globalData.systemInfo = systemInfo;
      this.globalData.statusBarHeight = statusBarHeight;
      this.globalData.navBarHeight = statusBarHeight + navBarHeight;
      
      console.log('系统信息:', {
        platform,
        model,
        system,
        statusBarHeight,
        navBarHeight: this.globalData.navBarHeight
      });
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  // 检查小程序更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate);
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        });
      });
    }
  },

  // 初始化用户信息
  initUserInfo() {
    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (token) {
      // 验证token有效性
      this.validateToken(token);
    }
  },

  // 验证token
  validateToken(token) {
    // 这里可以调用后端API验证token
    console.log('验证token:', token);
  },

  // 全局错误处理
  showError(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration
    });
  },

  // 全局成功提示
  showSuccess(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration
    });
  },

  // 全局加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading();
  },

  // 网络请求封装
  request(options) {
    const { url, method = 'GET', data = {}, header = {} } = options;
    
    return new Promise((resolve, reject) => {
      wx.request({
        url,
        method,
        data,
        header: {
          'Content-Type': 'application/json',
          ...header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`请求失败: ${res.statusCode}`));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo;
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    this.globalData.userInfo = userInfo;
  }
});
