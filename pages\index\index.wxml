<!--index.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px; height: {{navBarHeight}}px;">
    <view class="navbar-content">
      <view class="navbar-center">
        <text class="navbar-title">素材</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/images/search.svg"></image>
      <input class="search-input" placeholder="素材关键词" placeholder-class="search-placeholder" value="{{searchValue}}" bindinput="onSearchInput"/>
      <view class="search-btn" bindtap="onSearch">搜索</view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs {{categoryTabsSticky ? 'sticky' : ''}}" id="categoryTabs">
    <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" data-index="0" bindtap="switchTab">
        <text>每日爆品推荐</text>
        <view class="tab-underline"></view>
      </view>
      <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" data-index="1" bindtap="switchTab">
        <text>宣发/锁粉</text>
        <view class="tab-underline"></view>
      </view>
    </scroll-view>
  </view>

  <!-- 分类标签占位符 -->
  <view class="category-tabs-placeholder {{categoryTabsSticky ? 'show' : ''}}"></view>

  <!-- 二级分类 -->
  <view class="sub-category {{subCategorySticky ? 'sticky' : ''}}" id="subCategory">
    <scroll-view class="sub-tabs-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="sub-tab-item {{currentSubTab === 0 ? 'active' : ''}}" data-index="0" bindtap="switchSubTab">
        <text>家清日用</text>
      </view>
      <view class="sub-tab-item {{currentSubTab === 1 ? 'active' : ''}}" data-index="1" bindtap="switchSubTab">
        <text>食品</text>
      </view>
      <view class="sub-tab-item {{currentSubTab === 2 ? 'active' : ''}}" data-index="2" bindtap="switchSubTab">
        <text>个护</text>
      </view>
      <view class="sub-tab-item {{currentSubTab === 3 ? 'active' : ''}}" data-index="3" bindtap="switchSubTab">
        <text>家居生活</text>
      </view>
      <view class="sub-tab-item {{currentSubTab === 4 ? 'active' : ''}}" data-index="4" bindtap="switchSubTab">
        <text>母婴</text>
      </view>
      <view class="sub-tab-item {{currentSubTab === 5 ? 'active' : ''}}" data-index="5" bindtap="switchSubTab">
        <text>百货</text>
      </view>
      <view class="sub-tab-item {{currentSubTab === 6 ? 'active' : ''}}" data-index="6" bindtap="switchSubTab">
        <text>会员</text>
      </view>
    </scroll-view>
  </view>

  <!-- 子分类占位符 -->
  <view class="sub-category-placeholder {{subCategorySticky ? 'show' : ''}}"></view>

  <!-- 商品推荐列表 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="{{false}}" bindscroll="onPageScroll">
    <!-- 小店营推荐官 -->
    <view class="recommend-section">
      <view class="recommend-header">
        <image class="avatar" src="/images/avatar1.svg"></image>
        <view class="recommend-info">
          <text class="recommend-title">小店营推荐官</text>
          <text class="recommend-time">07月14日 17:01发布</text>
        </view>
      </view>
      
      <view class="recommend-content">
        <view class="highlight-text">
          <text>💥【强在朋友圈热推中，40包囤货装】💥</text>
        </view>
        <view class="highlight-text">
          <text>👍49.9买走全家人的夏天保护！</text>
        </view>
      </view>

      <!-- 商品卡片 -->
      <view class="product-card">
        <!-- 商品图片横向滑动 -->
        <scroll-view class="product-images-scroll" scroll-x="true" show-scrollbar="{{false}}">
          <view class="product-images-container">
            <image class="product-image-item" src="/images/product1.svg" bindtap="previewImage" data-current="/images/product1.svg" data-urls="{{['/images/product1.svg', '/images/product1-1.svg', '/images/product1-2.svg', '/images/product1-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product1-1.svg" bindtap="previewImage" data-current="/images/product1-1.svg" data-urls="{{['/images/product1.svg', '/images/product1-1.svg', '/images/product1-2.svg', '/images/product1-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product1-2.svg" bindtap="previewImage" data-current="/images/product1-2.svg" data-urls="{{['/images/product1.svg', '/images/product1-1.svg', '/images/product1-2.svg', '/images/product1-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product1-3.svg" bindtap="previewImage" data-current="/images/product1-3.svg" data-urls="{{['/images/product1.svg', '/images/product1-1.svg', '/images/product1-2.svg', '/images/product1-3.svg']}}"></image>
          </view>
        </scroll-view>

        <view class="product-info">
          <view class="product-title-section">
            <image class="product-title-icon" src="/images/product1.svg"></image>
            <view class="product-title">【买1送40包】明达驱蚊清香3件组...</view>
          </view>
          <view class="price-section">
            <view class="current-price">¥49.90</view>
            <view class="commission">¥3.49</view>
          </view>
          <view class="price-labels">
            <text class="original-price">到手价</text>
            <text class="commission-label">预估合伙人</text>
          </view>
        </view>
      </view>

      <!-- 评论内容 -->
      <view class="comment-section">
        <text class="comment-title">评论内容</text>
        <view class="comment-content">
          <text class="comment-text">👍高走下方链接下：【推广链接】</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons-row">
        <view class="action-btn" bindtap="onCopyComment" data-index="0">复制评论</view>
        <view class="action-btn" bindtap="onCopyText" data-index="0">复制文案</view>
        <view class="action-btn primary" bindtap="onSaveAndCopy" data-index="0">保存素材+复制文案</view>
      </view>
    </view>

    <!-- 第二个推荐 -->
    <view class="recommend-section">
      <view class="recommend-header">
        <image class="avatar" src="/images/avatar2.svg"></image>
        <view class="recommend-info">
          <text class="recommend-title">小店营推荐官</text>
          <text class="recommend-time">07月14日 14:50发布</text>
        </view>
      </view>
      
      <view class="recommend-content">
        <view class="highlight-text">
          <text>🔥【限时历史小心上】心相印素雅简市三红包来袭！</text>
        </view>
        <view class="highlight-text">
          <text>📦16片×3包保装，黄金包，质口感超值为先，印花局</text>
        </view>
      </view>

      <!-- 商品卡片 -->
      <view class="product-card">
        <!-- 商品图片横向滑动 -->
        <scroll-view class="product-images-scroll" scroll-x="true" show-scrollbar="{{false}}">
          <view class="product-images-container">
            <image class="product-image-item" src="/images/product2.svg" bindtap="previewImage" data-current="/images/product2.svg" data-urls="{{['/images/product2.svg', '/images/product2-1.svg', '/images/product2-2.svg', '/images/product2-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product2-1.svg" bindtap="previewImage" data-current="/images/product2-1.svg" data-urls="{{['/images/product2.svg', '/images/product2-1.svg', '/images/product2-2.svg', '/images/product2-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product2-2.svg" bindtap="previewImage" data-current="/images/product2-2.svg" data-urls="{{['/images/product2.svg', '/images/product2-1.svg', '/images/product2-2.svg', '/images/product2-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product2-3.svg" bindtap="previewImage" data-current="/images/product2-3.svg" data-urls="{{['/images/product2.svg', '/images/product2-1.svg', '/images/product2-2.svg', '/images/product2-3.svg']}}"></image>
          </view>
        </scroll-view>

        <view class="product-info">
          <view class="product-title-section">
            <image class="product-title-icon" src="/images/product2.svg"></image>
            <view class="product-title">心相印卫生素雅简市分装简市保装</view>
          </view>
          <view class="price-section">
            <view class="current-price">¥9.90</view>
            <view class="commission">¥0.83</view>
          </view>
          <view class="price-labels">
            <text class="original-price">到手价</text>
            <text class="commission-label">预估合伙人</text>
          </view>
        </view>
      </view>

      <!-- 评论内容 -->
      <view class="comment-section">
        <text class="comment-title">评论内容</text>
        <view class="comment-content">
          <text class="comment-text">👍高走下方链接下：【推广链接】</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons-row">
        <view class="action-btn" bindtap="onCopyComment" data-index="1">复制评论</view>
        <view class="action-btn" bindtap="onCopyText" data-index="1">复制文案</view>
        <view class="action-btn primary" bindtap="onSaveAndCopy" data-index="1">保存素材+复制文案</view>
      </view>
    </view>

    <!-- 第三个推荐 -->
    <view class="recommend-section">
      <view class="recommend-header">
        <image class="avatar" src="/images/avatar1.svg"></image>
        <view class="recommend-info">
          <text class="recommend-title">小店营推荐官</text>
          <text class="recommend-time">07月14日 12:30发布</text>
        </view>
      </view>

      <view class="recommend-content">
        <view class="highlight-text">
          <text>🔥【数码爆款限时抢购】🔥</text>
        </view>
        <view class="highlight-text">
          <text>💻高性能产品，品质保证，限量特价！</text>
        </view>
      </view>

      <!-- 商品卡片 -->
      <view class="product-card">
        <!-- 商品图片横向滑动 -->
        <scroll-view class="product-images-scroll" scroll-x="true" show-scrollbar="{{false}}">
          <view class="product-images-container">
            <image class="product-image-item" src="/images/product3.svg" bindtap="previewImage" data-current="/images/product3.svg" data-urls="{{['/images/product3.svg', '/images/product3-1.svg', '/images/product3-2.svg', '/images/product3-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product3-1.svg" bindtap="previewImage" data-current="/images/product3-1.svg" data-urls="{{['/images/product3.svg', '/images/product3-1.svg', '/images/product3-2.svg', '/images/product3-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product3-2.svg" bindtap="previewImage" data-current="/images/product3-2.svg" data-urls="{{['/images/product3.svg', '/images/product3-1.svg', '/images/product3-2.svg', '/images/product3-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product3-3.svg" bindtap="previewImage" data-current="/images/product3-3.svg" data-urls="{{['/images/product3.svg', '/images/product3-1.svg', '/images/product3-2.svg', '/images/product3-3.svg']}}"></image>
          </view>
        </scroll-view>

        <view class="product-info">
          <view class="product-title-section">
            <image class="product-title-icon" src="/images/product3.svg"></image>
            <view class="product-title">【限时特价】高性能数码产品套装...</view>
          </view>
          <view class="price-section">
            <view class="current-price">¥299.00</view>
            <view class="commission">¥29.90</view>
          </view>
          <view class="price-labels">
            <text class="original-price">到手价</text>
            <text class="commission-label">预估合伙人</text>
          </view>
        </view>
      </view>

      <!-- 评论内容 -->
      <view class="comment-section">
        <text class="comment-title">评论内容</text>
        <view class="comment-content">
          <text class="comment-text">🎯点击下方链接立即抢购：【推广链接】</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons-row">
        <view class="action-btn" bindtap="onCopyComment" data-index="2">复制评论</view>
        <view class="action-btn" bindtap="onCopyText" data-index="2">复制文案</view>
        <view class="action-btn primary" bindtap="onSaveAndCopy" data-index="2">保存素材+复制文案</view>
      </view>
    </view>

    <!-- 第四个推荐 -->
    <view class="recommend-section">
      <view class="recommend-header">
        <image class="avatar" src="/images/avatar2.svg"></image>
        <view class="recommend-info">
          <text class="recommend-title">小店营推荐官</text>
          <text class="recommend-time">07月14日 10:15发布</text>
        </view>
      </view>

      <view class="recommend-content">
        <view class="highlight-text">
          <text>✨【美妆新品上市】护肤套装大促销✨</text>
        </view>
        <view class="highlight-text">
          <text>🌸温和配方，适合所有肌肤，买2送1！</text>
        </view>
      </view>

      <!-- 商品卡片 -->
      <view class="product-card">
        <!-- 商品图片横向滑动 -->
        <scroll-view class="product-images-scroll" scroll-x="true" show-scrollbar="{{false}}">
          <view class="product-images-container">
            <image class="product-image-item" src="/images/product4.svg" bindtap="previewImage" data-current="/images/product4.svg" data-urls="{{['/images/product4.svg', '/images/product4-1.svg', '/images/product4-2.svg', '/images/product4-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product4-1.svg" bindtap="previewImage" data-current="/images/product4-1.svg" data-urls="{{['/images/product4.svg', '/images/product4-1.svg', '/images/product4-2.svg', '/images/product4-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product4-2.svg" bindtap="previewImage" data-current="/images/product4-2.svg" data-urls="{{['/images/product4.svg', '/images/product4-1.svg', '/images/product4-2.svg', '/images/product4-3.svg']}}"></image>
            <image class="product-image-item" src="/images/product4-3.svg" bindtap="previewImage" data-current="/images/product4-3.svg" data-urls="{{['/images/product4.svg', '/images/product4-1.svg', '/images/product4-2.svg', '/images/product4-3.svg']}}"></image>
          </view>
        </scroll-view>

        <view class="product-info">
          <view class="product-title-section">
            <image class="product-title-icon" src="/images/product4.svg"></image>
            <view class="product-title">【买2送1】温和护肤套装礼盒装</view>
          </view>
          <view class="price-section">
            <view class="current-price">¥168.00</view>
            <view class="commission">¥16.80</view>
          </view>
          <view class="price-labels">
            <text class="original-price">到手价</text>
            <text class="commission-label">预估合伙人</text>
          </view>
        </view>
      </view>

      <!-- 评论内容 -->
      <view class="comment-section">
        <text class="comment-title">评论内容</text>
        <view class="comment-content">
          <text class="comment-text">💄美丽从这里开始：【推广链接】</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons-row">
        <view class="action-btn" bindtap="onCopyComment" data-index="3">复制评论</view>
        <view class="action-btn" bindtap="onCopyText" data-index="3">复制文案</view>
        <view class="action-btn primary" bindtap="onSaveAndCopy" data-index="3">保存素材+复制文案</view>
      </view>
    </view>
  </scroll-view>
</view>
