/**app.wxss**/
page {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 全局重置样式 */
view, text, image, input, button, scroll-view {
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.btn-primary:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(255, 107, 53, 0.3);
}

.btn-secondary {
  background-color: white;
  color: #666;
  border: 1px solid #ddd;
}

.btn-secondary:active {
  background-color: #f5f5f5;
}

/* 通用卡片样式 */
.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 通用间距 */
.mt-8 { margin-top: 8px; }
.mt-16 { margin-top: 16px; }
.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.ml-8 { margin-left: 8px; }
.mr-8 { margin-right: 8px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }
.px-8 { padding-left: 8px; padding-right: 8px; }
.px-16 { padding-left: 16px; padding-right: 16px; }
.py-8 { padding-top: 8px; padding-bottom: 8px; }
.py-16 { padding-top: 16px; padding-bottom: 16px; }

/* 通用布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

/* 通用文本样式 */
.text-primary {
  color: #ff6b35;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: 600;
}

.text-small {
  font-size: 12px;
}

.text-large {
  font-size: 16px;
}

/* 滚动条隐藏 - 微信小程序通过组件属性控制 */

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #999;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
  color: #999;
}
