// index.js
Page({
  data: {
    statusBarHeight: 0,
    navBarHeight: 0,
    currentTab: 0,
    currentSubTab: 0,
    searchValue: '',
    categoryTabsSticky: false,
    subCategorySticky: false,
    recommendList: [
      {
        id: 1,
        avatar: '/images/avatar1.svg',
        title: '小店营推荐官',
        time: '07月14日 17:01发布',
        content: [
          '💥【强在朋友圈热推中，40包囤货装】💥',
          '👍49.9买走全家人的夏天保护！'
        ],
        product: {
          title: '【买1送40包】明达驱蚊清香3件组...',
          price: '49.90',
          commission: '3.49',
          images: [
            '/images/product1.svg',
            '/images/product1-1.svg',
            '/images/product1-2.svg',
            '/images/product1-3.svg'
          ]
        },
        shop: '许氏分店',
        tip: '👍高走下方链接下：【推广链接】'
      },
      {
        id: 2,
        avatar: '/images/avatar2.svg',
        title: '小店营推荐官',
        time: '07月14日 14:50发布',
        content: [
          '🔥【限时历史小心上】心相印素雅简市三红包来袭！',
          '📦16片×3包保装，黄金包，质口感超值为先，印花局'
        ],
        product: {
          title: '心相印卫生素雅简市分装简市保装',
          price: '9.90',
          commission: '0.83',
          images: [
            '/images/product2.svg',
            '/images/product2-1.svg',
            '/images/product2-2.svg',
            '/images/product2-3.svg'
          ]
        },
        shop: '许氏分店',
        tip: '👍高走下方链接下：【推广链接】'
      },
      {
        id: 3,
        avatar: '/images/avatar1.svg',
        title: '小店营推荐官',
        time: '07月14日 12:30发布',
        content: [
          '🔥【数码爆款限时抢购】🔥',
          '💻高性能产品，品质保证，限量特价！'
        ],
        product: {
          title: '【限时特价】高性能数码产品套装...',
          price: '299.00',
          commission: '29.90',
          images: [
            '/images/product3.svg',
            '/images/product3-1.svg',
            '/images/product3-2.svg',
            '/images/product3-3.svg'
          ]
        },
        shop: '数码专营店',
        tip: '🎯点击下方链接立即抢购：【推广链接】'
      },
      {
        id: 4,
        avatar: '/images/avatar2.svg',
        title: '小店营推荐官',
        time: '07月14日 10:15发布',
        content: [
          '✨【美妆新品上市】护肤套装大促销✨',
          '🌸温和配方，适合所有肌肤，买2送1！'
        ],
        product: {
          title: '【买2送1】温和护肤套装礼盒装',
          price: '168.00',
          commission: '16.80',
          images: [
            '/images/product4.svg',
            '/images/product4-1.svg',
            '/images/product4-2.svg',
            '/images/product4-3.svg'
          ]
        },
        shop: '美妆旗舰店',
        tip: '💄美丽从这里开始：【推广链接】'
      }
    ]
  },

  onLoad() {
    this.getSystemInfo();
  },

  onReady() {
    this.initScrollListener();
  },

  // 获取系统信息，设置自定义导航栏高度
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const { statusBarHeight, platform } = systemInfo;

    // 不同平台导航栏高度不同
    let navBarHeight = 44;
    if (platform === 'ios') {
      navBarHeight = 44;
    } else if (platform === 'android') {
      navBarHeight = 48;
    }

    this.setData({
      statusBarHeight,
      navBarHeight: statusBarHeight + navBarHeight
    });
  },

  // 切换主分类
  switchTab(e) {
    const index = parseInt(e.currentTarget.dataset.index);

    // 防止重复点击
    if (this.data.currentTab === index) {
      return;
    }

    // 添加切换动画效果
    try {
      wx.vibrateShort({
        type: 'light'
      });
    } catch (error) {
      console.log('震动反馈不支持');
    }

    this.setData({
      currentTab: index,
      currentSubTab: 0 // 重置子分类
    });

    // 模拟加载数据
    this.loadTabData(index);
  },

  // 切换子分类
  switchSubTab(e) {
    const index = parseInt(e.currentTarget.dataset.index);

    // 防止重复点击
    if (this.data.currentSubTab === index) {
      return;
    }

    // 添加切换动画效果
    try {
      wx.vibrateShort({
        type: 'light'
      });
    } catch (error) {
      console.log('震动反馈不支持');
    }

    this.setData({
      currentSubTab: index
    });

    // 模拟加载数据
    this.loadSubTabData(index);
  },

  // 加载主分类数据
  loadTabData(tabIndex) {
    // 防止频繁加载
    if (this.loadingTab) {
      return;
    }

    this.loadingTab = true;

    // 模拟网络请求
    setTimeout(() => {
      this.loadingTab = false;
      console.log('加载主分类数据:', tabIndex);
      // 这里可以根据tabIndex加载不同的数据
    }, 300);
  },

  // 加载子分类数据
  loadSubTabData(subTabIndex) {
    console.log('加载子分类数据:', subTabIndex);
    // 这里可以根据subTabIndex过滤数据
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    const { searchValue } = this.data;
    if (!searchValue.trim()) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    console.log('搜索关键词:', searchValue);
    // 这里可以调用搜索API
  },

  // 复制评论
  onCopyComment(e) {
    const index = e.currentTarget.dataset.index;
    wx.setClipboardData({
      data: '复制的评论内容',
      success: () => {
        wx.showToast({
          title: '评论已复制',
          icon: 'success'
        });
      }
    });
  },

  // 复制文案
  onCopyText(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.recommendList[index];
    const copyText = item.content.join('\n') + '\n' + item.product.title;

    wx.setClipboardData({
      data: copyText,
      success: () => {
        wx.showToast({
          title: '文案已复制',
          icon: 'success'
        });
      }
    });
  },

  // 保存素材并复制文案
  onSaveAndCopy(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.recommendList[index];

    // 先保存素材
    this.saveMaterial(item);

    // 再复制文案
    const copyText = item.content.join('\n') + '\n' + item.product.title;
    wx.setClipboardData({
      data: copyText,
      success: () => {
        wx.showToast({
          title: '素材已保存，文案已复制',
          icon: 'success'
        });
      }
    });
  },

  // 保存素材
  saveMaterial(item) {
    // 这里可以调用保存素材的API
    console.log('保存素材:', item);
  },

  // 预览图片
  previewImage(e) {
    const { current, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current,
      urls
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 模拟刷新数据
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom() {
    wx.showLoading({
      title: '加载中...'
    });

    // 模拟加载更多数据
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '暂无更多数据',
        icon: 'none'
      });
    }, 1000);
  },

  // 页面滚动监听
  onPageScroll(e) {
    const scrollTop = e.detail.scrollTop;

    // 计算关键位置
    const searchBarHeight = 56; // 搜索栏实际高度
    const categoryTabsHeight = 48; // 分类标签高度
    const subCategoryHeight = 44; // 子分类高度

    // 分类标签置顶判断 - 当滚动超过搜索栏时
    const categoryTabsThreshold = searchBarHeight;
    const categoryTabsSticky = scrollTop >= categoryTabsThreshold;

    // 子分类置顶判断 - 当滚动超过搜索栏+分类标签时
    const subCategoryThreshold = searchBarHeight + categoryTabsHeight + 8; // 8px是margin
    const subCategorySticky = scrollTop >= subCategoryThreshold;

    // 批量更新状态，避免频繁setData
    const updates = {};

    if (this.data.categoryTabsSticky !== categoryTabsSticky) {
      updates.categoryTabsSticky = categoryTabsSticky;
    }

    if (this.data.subCategorySticky !== subCategorySticky) {
      updates.subCategorySticky = subCategorySticky;
    }

    // 只有状态变化时才更新
    if (Object.keys(updates).length > 0) {
      this.setData(updates);
      console.log('Sticky状态更新:', updates, 'scrollTop:', scrollTop);
    }
  },

  // 初始化滚动监听
  initScrollListener() {
    // 使用页面滚动事件，更可靠
    console.log('滚动监听已初始化');
  },

  onUnload() {
    // 页面卸载时的清理工作
    console.log('页面卸载');
  }
});