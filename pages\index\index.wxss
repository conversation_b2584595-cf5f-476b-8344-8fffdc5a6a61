/* index.wxss */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: #f5f5f5;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  height: 44px;
}

.navbar-center {
  display: flex;
  align-items: center;
}

.navbar-title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

/* 搜索栏 */
.search-container {
  margin-top: 88px; /* 导航栏高度 + 状态栏高度 */
  padding: 8px 16px;
  background-color: #f5f5f5;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 20px;
  padding: 6px 12px;
  border: 2px solid #ff6b35;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  font-size: 14px;
}

.search-placeholder {
  color: #999;
}

.search-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 14px;
  margin-left: 8px;
}

/* 分类标签 */
.category-tabs {
  background-color: #f5f5f5;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.category-tabs.sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #f5f5f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 分类标签占位符 */
.category-tabs-placeholder {
  height: 0;
  transition: height 0.3s ease;
}

.category-tabs-placeholder.show {
  height: 48px;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-scroll::-webkit-scrollbar {
  display: none;
}

.tab-item {
  display: inline-block;
  padding: 8px 16px;
  margin: 0 8px;
  font-size: 16px;
  color: #333;
  transition: color 0.2s ease;
  position: relative;
  cursor: pointer;
}

.tab-item:active {
  opacity: 0.7;
}

.tab-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 2px;
  transform: translateX(-50%);
  transition: width 0.2s ease-out;
}

.tab-item.active {
  color: #ff6b35;
  font-weight: 600;
}

.tab-item.active .tab-underline {
  width: 60%;
}

/* 二级分类 */
.sub-category {
  background-color: #f5f5f5;
  padding: 8px 0 12px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.sub-category.sticky {
  position: fixed;
  top: 48px; /* 主分类高度 */
  left: 0;
  right: 0;
  z-index: 998;
  background-color: #f5f5f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 子分类占位符 */
.sub-category-placeholder {
  height: 0;
  transition: height 0.3s ease;
}

.sub-category-placeholder.show {
  height: 44px;
}

.sub-tabs-scroll {
  white-space: nowrap;
}

.sub-tabs-scroll::-webkit-scrollbar {
  display: none;
}

.sub-tab-item {
  display: inline-block;
  padding: 6px 12px;
  margin: 0 4px;
  font-size: 14px;
  color: #666;
  border-radius: 16px;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
}

.sub-tab-item:active {
  opacity: 0.7;
}

.sub-tab-item.active {
  background-color: #fff2e6;
  color: #ff6b35;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(255, 107, 53, 0.2);
  border: 1px solid rgba(255, 107, 53, 0.2);
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  padding: 0 16px;
}

/* 推荐区域 */
.recommend-section {
  background-color: white;
  border-radius: 8px;
  margin: 16px 0;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  transform: translateY(0);
}

.recommend-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.recommend-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
}

.recommend-info {
  flex: 1;
}

.recommend-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.recommend-time {
  font-size: 12px;
  color: #999;
}

.recommend-content {
  margin-bottom: 16px;
}

.highlight-text {
  margin-bottom: 8px;
}

.highlight-text text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

/* 商品卡片 */
.product-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

/* 商品图片横向滑动 */
.product-images-scroll {
  white-space: nowrap;
  background-color: #fafafa;
  padding: 8px;
}

.product-images-scroll::-webkit-scrollbar {
  display: none;
}

.product-images-container {
  display: inline-flex;
}

.product-image-item {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  margin-right: 8px;
  flex-shrink: 0;
}

.product-image-item:last-child {
  margin-right: 0;
}

.product-info {
  padding: 12px;
}

.product-title-section {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.product-title-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  border-radius: 4px;
}

.product-title {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

.price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
}

.price-section .commission {
  margin-left: 12px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #ff6b35;
}

.commission {
  font-size: 16px;
  font-weight: 600;
  color: #ff6b35;
}

.price-labels {
  display: flex;
}

.price-labels .commission-label {
  margin-left: 12px;
}

.original-price,
.commission-label {
  font-size: 12px;
  color: #999;
}

/* 评论内容 */
.comment-section {
  margin: 12px 0;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.comment-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.comment-content {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.comment-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 操作按钮行 */
.action-buttons-row {
  display: flex;
  justify-content: flex-start;
  margin-top: 12px;
}

.action-buttons-row .action-btn:not(:last-child) {
  margin-right: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  background-color: white;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.action-btn:active::before {
  width: 100px;
  height: 100px;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.action-btn.primary::before {
  background: rgba(255, 255, 255, 0.2);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 响应式适配 - 微信小程序通过rpx单位和js动态设置实现 */
